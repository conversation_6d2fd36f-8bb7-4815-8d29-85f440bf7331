<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ProfileController;
use Illuminate\Support\Facades\Broadcast;
use App\Http\Controllers\MessageController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Test auth route
Route::middleware('auth:sanctum')->get('/test-auth', function (Request $request) {
    $user = $request->user();
    return response()->json([
        'authenticated' => !!$user,
        'user_id' => $user ? $user->id : null,
        'username' => $user ? $user->username : null
    ]);
});

Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');

// Custom broadcasting auth route with debugging
// Route::post('/broadcasting/auth', function (Request $request) {
//     error_log('Broadcasting auth request received');
//     error_log('User: ' . ($request->user() ? $request->user()->id : 'none'));
//     error_log('Channel: ' . $request->input('channel_name'));
//     error_log('Socket ID: ' . $request->input('socket_id'));

//     try {
//         $result = Broadcast::auth($request);
//         error_log('Broadcasting auth successful');
//         return $result;
//     } catch (\Exception $e) {
//         error_log('Broadcasting auth failed: ' . $e->getMessage());
//         error_log('Exception type: ' . get_class($e));
//         throw $e;
//     }
// })->middleware('auth:sanctum');

// Register other broadcasting routes
//Broadcast::routes(['middleware' => ['auth:sanctum'], 'prefix' => 'broadcasting', 'as' => 'broadcasting.']);

// Message routes
Route::get('/messages/history', [MessageController::class, 'history']); // Public route for chat history
Route::middleware('auth:sanctum')->group(function () {
    Route::post('/messages', [MessageController::class, 'send']);
});

// Profile routes
Route::middleware('auth:sanctum')->group(function () {
    Route::put('/profile', [ProfileController::class, 'updateProfile']);
    Route::put('/profile/password', [ProfileController::class, 'updatePassword']);
    Route::post('/profile/image', [ProfileController::class, 'updateProfileImage']);
    Route::delete('/profile/image', [ProfileController::class, 'deleteProfileImage']);
});
